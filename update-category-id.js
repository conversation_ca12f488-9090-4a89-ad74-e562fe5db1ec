const mysql = require('mysql2/promise');
require('dotenv').config({ path: '.env.local' });

async function updateCategoryId() {
  let connection;

  try {
    // إنشاء الاتصال بقاعدة البيانات
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'droobhajer',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'droobhajer_db'
    });

    console.log('🔗 تم الاتصال بقاعدة البيانات');

    // 1. إلغاء التحقق من المفاتيح الخارجية مؤقتاً
    console.log('🔓 إلغاء التحقق من المفاتيح الخارجية...');
    await connection.execute('SET FOREIGN_KEY_CHECKS = 0');

    // 2. التحقق من وجود الفئة F_B
    console.log('🔍 البحث عن الفئة F_B...');
    const [existingCategory] = await connection.execute(
      'SELECT id, name, name_ar FROM categories WHERE id = ?',
      ['F_B']
    );

    if (existingCategory.length === 0) {
      console.log('❌ لم يتم العثور على الفئة F_B');
      return;
    }

    console.log(`✅ تم العثور على الفئة: ${existingCategory[0].name_ar} (${existingCategory[0].name})`);

    // 3. التحقق من عدم وجود فئة بالـ ID الجديد
    const [conflictCategory] = await connection.execute(
      'SELECT id FROM categories WHERE id = ?',
      ['Food-Beverage']
    );

    if (conflictCategory.length > 0) {
      console.log('❌ يوجد فئة أخرى بالـ ID الجديد Food-Beverage');
      return;
    }

    // 4. جلب الفئات الفرعية المرتبطة
    console.log('📋 جلب الفئات الفرعية المرتبطة...');
    const [subcategories] = await connection.execute(
      'SELECT id, name, name_ar FROM subcategories WHERE category_id = ?',
      ['F_B']
    );

    console.log(`📦 تم العثور على ${subcategories.length} فئة فرعية`);
    subcategories.forEach(sub => {
      console.log(`   - ${sub.name_ar} (${sub.name}) - ID: ${sub.id}`);
    });

    // 5. جلب المنتجات المرتبطة
    console.log('🛍️ جلب المنتجات المرتبطة...');
    const [products] = await connection.execute(
      'SELECT id, title, title_ar FROM products WHERE category_id = ?',
      ['F_B']
    );

    console.log(`📦 تم العثور على ${products.length} منتج`);

    // 6. تحديث ID الفئة الرئيسية
    console.log('🔄 تحديث ID الفئة الرئيسية...');
    await connection.execute(
      'UPDATE categories SET id = ? WHERE id = ?',
      ['Food-Beverage', 'F_B']
    );
    console.log('✅ تم تحديث ID الفئة الرئيسية من F_B إلى Food-Beverage');

    // 7. تحديث الفئات الفرعية
    if (subcategories.length > 0) {
      console.log('🔄 تحديث الفئات الفرعية...');
      await connection.execute(
        'UPDATE subcategories SET category_id = ? WHERE category_id = ?',
        ['Food-Beverage', 'F_B']
      );
      console.log(`✅ تم تحديث ${subcategories.length} فئة فرعية`);
    }

    // 8. تحديث المنتجات
    if (products.length > 0) {
      console.log('🔄 تحديث المنتجات...');
      await connection.execute(
        'UPDATE products SET category_id = ? WHERE category_id = ?',
        ['Food-Beverage', 'F_B']
      );
      console.log(`✅ تم تحديث ${products.length} منتج`);
    }

    // 9. إعادة تفعيل التحقق من المفاتيح الخارجية
    console.log('🔒 إعادة تفعيل التحقق من المفاتيح الخارجية...');
    await connection.execute('SET FOREIGN_KEY_CHECKS = 1');

    // 10. التحقق من النتائج النهائية
    console.log('\n📊 التحقق من النتائج النهائية...');
    
    const [updatedCategory] = await connection.execute(
      'SELECT id, name, name_ar FROM categories WHERE id = ?',
      ['Food-Beverage']
    );

    if (updatedCategory.length > 0) {
      console.log(`✅ الفئة الجديدة: ${updatedCategory[0].name_ar} (${updatedCategory[0].name}) - ID: ${updatedCategory[0].id}`);
    }

    const [updatedSubcategories] = await connection.execute(
      'SELECT COUNT(*) as count FROM subcategories WHERE category_id = ?',
      ['Food-Beverage']
    );

    const [updatedProducts] = await connection.execute(
      'SELECT COUNT(*) as count FROM products WHERE category_id = ?',
      ['Food-Beverage']
    );

    console.log(`✅ عدد الفئات الفرعية المحدثة: ${updatedSubcategories[0].count}`);
    console.log(`✅ عدد المنتجات المحدثة: ${updatedProducts[0].count}`);

    console.log('\n🎉 تم تحديث ID الفئة بنجاح!');
    console.log('🔗 الرابط الجديد سيكون: /ar/category/Food-Beverage');

  } catch (error) {
    console.error('❌ خطأ في تحديث ID الفئة:', error);
    
    // التأكد من إعادة تفعيل المفاتيح الخارجية في حالة الخطأ
    if (connection) {
      try {
        await connection.execute('SET FOREIGN_KEY_CHECKS = 1');
        console.log('🔒 تم إعادة تفعيل التحقق من المفاتيح الخارجية');
      } catch (fkError) {
        console.error('❌ خطأ في إعادة تفعيل المفاتيح الخارجية:', fkError);
      }
    }
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 تم إغلاق الاتصال بقاعدة البيانات');
    }
  }
}

// تشغيل السكريبت
updateCategoryId();
